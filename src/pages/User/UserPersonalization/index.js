import React from "react";
import { Container, <PERSON>, <PERSON> } from "react-bootstrap";
import "./UserPersonalization.scss";

function UserPersonalization() {
  return (
    <div className="mainContent">
      <div className="user-personalization">
        {/* Header Section */}
        <div className="personalization-header">
          <Container>
            <Row>
              <Col>
                <h1>User Personalization</h1>
                <p>Customize your experience and preferences</p>
              </Col>
            </Row>
          </Container>
        </div>

        {/* Main Layout with Sidebar and Content */}
        <div className="personalization-layout">
          <Container fluid>
            <Row className="h-100">
              {/* Left Sidebar */}
              <Col lg={3} md={4} className="personalization-sidebar">
                <div className="sidebar-content">
                  <h3>Settings</h3>
                  <nav className="sidebar-nav">
                    <ul className="nav-list">
                      <li className="nav-item active">
                        <a href="#profile" className="nav-link">
                          <i className="icon-user"></i>
                          Profile Settings
                        </a>
                      </li>
                      <li className="nav-item">
                        <a href="#preferences" className="nav-link">
                          <i className="icon-settings"></i>
                          Preferences
                        </a>
                      </li>
                      <li className="nav-item">
                        <a href="#notifications" className="nav-link">
                          <i className="icon-bell"></i>
                          Notifications
                        </a>
                      </li>
                      <li className="nav-item">
                        <a href="#privacy" className="nav-link">
                          <i className="icon-shield"></i>
                          Privacy
                        </a>
                      </li>
                      <li className="nav-item">
                        <a href="#appearance" className="nav-link">
                          <i className="icon-palette"></i>
                          Appearance
                        </a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Col>

              {/* Main Content Area */}
              <Col lg={9} md={8} className="personalization-main">
                <div className="main-content">
                  <div className="content-header">
                    <h2>Profile Settings</h2>
                    <p>Manage your personal information and account details</p>
                  </div>

                  <div className="content-body">
                    {/* Content will be dynamically loaded based on sidebar selection */}
                    <div className="settings-section">
                      <h4>Personal Information</h4>
                      <p>Update your profile details, contact information, and more.</p>

                      {/* Placeholder content */}
                      <div className="form-section">
                        <div className="form-group">
                          <label>Full Name</label>
                          <input type="text" className="form-control" placeholder="Enter your full name" />
                        </div>
                        <div className="form-group">
                          <label>Email Address</label>
                          <input type="email" className="form-control" placeholder="Enter your email" />
                        </div>
                        <div className="form-group">
                          <label>Phone Number</label>
                          <input type="tel" className="form-control" placeholder="Enter your phone number" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </Container>
        </div>
      </div>
    </div>
  );
}

export default UserPersonalization;
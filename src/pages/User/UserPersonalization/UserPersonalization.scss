// Color variables
$primary-color: #007bff;
$secondary-color: #6c757d;
$background-color: #f8f9fa;
$sidebar-bg: #ffffff;
$border-color: #dee2e6;
$text-color: #333333;
$text-muted: #6c757d;
$hover-color: #f1f3f4;
$active-color: #e3f2fd;

.user-personalization {
  min-height: 100vh;
  background-color: $background-color;

  // Header Section
  .personalization-header {
    padding: 1.5rem;
    margin: 0;
    border-bottom: 1px solid $border-color;
    background-color: white;

    h1 {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0;
      color: $text-color;

      @media (max-width: 768px) {
        font-size: 1.25rem;
      }
    }
  }

  // Main Layout
  .personalization-layout {
    .container-fluid {
      padding: 0;
    }

    .row {
      margin: 0;
      min-height: calc(100vh - 200px);
    }
  }

  // Sidebar Styles
  .personalization-sidebar {
    background-color: $sidebar-bg;
    border-right: 1px solid $border-color;
    padding: 0;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);

    .sidebar-content {
      padding: 1.5rem;
      height: 100%;
    }

    .sidebar-nav {
      .nav-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .nav-item {
          margin-bottom: 0.5rem;

          .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: $text-color;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;

            i {
              margin-right: 0.75rem;
              font-size: 1.1rem;
              width: 20px;
              text-align: center;
            }

            &:hover {
              background-color: $hover-color;
              color: $primary-color;
              transform: translateX(4px);
            }
          }

          &.active .nav-link {
            background-color: $active-color;
            color: $primary-color;
            font-weight: 600;
            border-left: 4px solid $primary-color;
          }
        }
      }
    }
  }

  // Main Content Styles
  .personalization-main {
    padding: 0;
    background-color: $background-color;

    .main-content {
      padding: 2rem;
      height: 100%;

      .content-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid $border-color;

        h2 {
          font-size: 1.75rem;
          font-weight: 600;
          color: $text-color;
          margin-bottom: 0.5rem;
        }

        p {
          color: $text-muted;
          font-size: 1rem;
          margin-bottom: 0;
        }
      }

      .content-body {
        .settings-section {
          background-color: white;
          border-radius: 12px;
          padding: 2rem;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border: 1px solid $border-color;

          h4 {
            font-size: 1.25rem;
            font-weight: 600;
            color: $text-color;
            margin-bottom: 1rem;
          }

          p {
            color: $text-muted;
            margin-bottom: 1.5rem;
          }

          .form-section {
            .form-group {
              margin-bottom: 1.5rem;

              label {
                display: block;
                font-weight: 500;
                color: $text-color;
                margin-bottom: 0.5rem;
                font-size: 0.95rem;
              }

              .form-control {
                width: 100%;
                padding: 0.75rem 1rem;
                border: 1px solid $border-color;
                border-radius: 8px;
                font-size: 1rem;
                transition: all 0.3s ease;
                background-color: white;

                &:focus {
                  outline: none;
                  border-color: $primary-color;
                  box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
                }

                &::placeholder {
                  color: $text-muted;
                }
              }
            }
          }
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 992px) {
    .personalization-sidebar {
      border-right: none;
      border-bottom: 1px solid $border-color;

      .sidebar-content {
        padding: 1.5rem;

        .sidebar-nav .nav-list {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;

          .nav-item {
            margin-bottom: 0;
            flex: 1;
            min-width: 150px;

            .nav-link {
              justify-content: center;
              text-align: center;
              padding: 0.5rem;

              i {
                margin-right: 0.5rem;
              }
            }
          }
        }
      }
    }

    .personalization-main .main-content {
      padding: 1.5rem;
    }
  }

  @media (max-width: 768px) {
    .personalization-header {
      padding: 1.5rem 0;
    }

    .personalization-sidebar .sidebar-content {
      padding: 1rem;

      .sidebar-nav .nav-list {
        flex-direction: column;

        .nav-item {
          min-width: auto;

          .nav-link {
            justify-content: flex-start;
            text-align: left;
          }
        }
      }
    }

    .personalization-main .main-content {
      padding: 1rem;

      .content-body .settings-section {
        padding: 1.5rem;
      }
    }
  }
}
